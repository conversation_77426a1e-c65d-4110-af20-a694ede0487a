import { Ionicons } from '@expo/vector-icons';
import React, { useEffect, useRef, useState } from 'react';
import {
    Animated,
    Dimensions,
    KeyboardAvoidingView,
    Modal,
    Platform,
    StyleSheet,
    Text,
    TextInput,
    TouchableOpacity,
    TouchableWithoutFeedback,
    View,
} from 'react-native';
import { apiService } from '../../services/api';
import { notificationService } from '../../services/notificationService';

const { width, height } = Dimensions.get('window');

interface OTPModalProps {
  visible: boolean;
  email: string;
  onVerificationSuccess: () => void;
  onClose: () => void;
}

export const OTPModal: React.FC<OTPModalProps> = ({
  visible,
  email,
  onVerificationSuccess,
  onClose,
}) => {
  const [otp, setOtp] = useState(['', '', '', '', '', '']);
  const [isLoading, setIsLoading] = useState(false);
  const [resendCooldown, setResendCooldown] = useState(0);
  const [otpError, setOtpError] = useState('');

  // Animation values
  const slideAnim = useRef(new Animated.Value(height)).current;
  const backdropAnim = useRef(new Animated.Value(0)).current;

  // Input refs for focus management
  const inputRefs = useRef<(TextInput | null)[]>([]);

  // Cooldown timer
  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (resendCooldown > 0) {
      interval = setInterval(() => {
        setResendCooldown((prev) => prev - 1);
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [resendCooldown]);

  // Animation effects
  useEffect(() => {
    if (visible) {
      // Show modal with slide-up animation
      Animated.parallel([
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(backdropAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      // Hide modal with slide-down animation
      Animated.parallel([
        Animated.timing(slideAnim, {
          toValue: height,
          duration: 250,
          useNativeDriver: true,
        }),
        Animated.timing(backdropAnim, {
          toValue: 0,
          duration: 250,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [visible]);

  // Reset state when modal opens
  useEffect(() => {
    if (visible) {
      setOtp(['', '', '', '', '', '']);
      setOtpError('');
      setIsLoading(false);
      // Focus first input after a short delay
      setTimeout(() => {
        inputRefs.current[0]?.focus();
      }, 400);
    }
  }, [visible]);

  const handleOtpChange = (value: string, index: number) => {
    // Only allow numbers
    if (!/^\d*$/.test(value)) return;

    const newOtp = [...otp];
    newOtp[index] = value;
    setOtp(newOtp);
    setOtpError(''); // Clear error when user types

    // Auto-focus next input
    if (value && index < 5) {
      inputRefs.current[index + 1]?.focus();
    }
  };

  const handleKeyPress = (key: string, index: number) => {
    if (key === 'Backspace' && !otp[index] && index > 0) {
      // Focus previous input on backspace
      inputRefs.current[index - 1]?.focus();
    }
  };

  const handleVerifyOTP = async () => {
    const otpString = otp.join('');

    // Validation
    if (!otpString || otpString.length === 0) {
      notificationService.error('🔢 OTP Required', 'OTP daal! Code enter kar! 🔢');
      inputRefs.current[0]?.focus();
      return;
    }

    if (otpString.length !== 6) {
      notificationService.error('🔢 Incomplete OTP', `Pura 6-digit code daal! Abhi ${otpString.length} digits hai! 🔢`);
      inputRefs.current[otpString.length]?.focus();
      return;
    }

    if (!/^\d{6}$/.test(otpString)) {
      notificationService.error('🔢 Invalid OTP Format', 'Sirf numbers daal! Letters nahi! 📱');
      setOtp(['', '', '', '', '', '']);
      inputRefs.current[0]?.focus();
      return;
    }

    try {
      setIsLoading(true);
      setOtpError('');

      console.log('OTPModal: Verifying OTP for email:', email);
      const response = await apiService.verifyEmailOTP({
        email: email,
        otp: otpString,
      });

      if (response.success) {
        console.log('OTPModal: Verification successful');
        notificationService.success('🎉 Email verified! Ab login kar sakte hai! 🚀');
        
        // Close modal and redirect after success message
        setTimeout(() => {
          onVerificationSuccess();
        }, 1500);
      } else {
        throw new Error(response.message || 'Verification failed');
      }
    } catch (error: any) {
      console.error('OTPModal: Verification error:', error);

      let errorMessage = 'OTP verify nahi hua! Try again kar! 🔄';
      if (error.message?.includes('Invalid') || error.message?.includes('invalid')) {
        errorMessage = '🔢 OTP galat hai! Sahi code daal! Check kar email mein! 📧';
      } else if (error.message?.includes('expired') || error.message?.includes('Expired')) {
        errorMessage = '⏰ OTP expire ho gaya! Naya OTP mangwa! Time khatam! 🔄';
      }

      setOtpError(errorMessage);
      notificationService.error('❌ OTP Verification Failed', errorMessage);
      
      // Clear OTP and focus first input
      setOtp(['', '', '', '', '', '']);
      setTimeout(() => {
        inputRefs.current[0]?.focus();
      }, 100);
    } finally {
      setIsLoading(false);
    }
  };

  const handleResendOTP = async () => {
    if (resendCooldown > 0) return;

    try {
      setIsLoading(true);
      console.log('OTPModal: Resending OTP for email:', email);

      const response = await apiService.resendVerificationOTP(email);
      if (response.success) {
        notificationService.success(`📧 OTP Sent! ✅ Naya OTP bhej diya! ${email} pe check kar! 📱`);
        setResendCooldown(60); // 60 second cooldown
        setOtp(['', '', '', '', '', '']);
        setOtpError('');
        inputRefs.current[0]?.focus();
      } else {
        throw new Error(response.message || 'Failed to resend OTP');
      }
    } catch (error: any) {
      console.error('OTPModal: Resend error:', error);
      const errorMessage = error.message || 'OTP resend nahi hua! Network check kar! 🌐';
      notificationService.error('❌ Resend Failed', errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    setOtp(['', '', '', '', '', '']);
    setOtpError('');
    onClose();
  };

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="none"
      onRequestClose={handleClose}
    >
      <TouchableWithoutFeedback onPress={handleClose}>
        <Animated.View style={[styles.backdrop, { opacity: backdropAnim }]}>
          <TouchableWithoutFeedback onPress={() => {}}>
            <KeyboardAvoidingView
              behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
              style={styles.keyboardAvoidingView}
            >
              <Animated.View
                style={[
                  styles.modalContainer,
                  {
                    transform: [{ translateY: slideAnim }],
                  },
                ]}
              >
                {/* Header */}
                <View style={styles.header}>
                  <View style={styles.dragIndicator} />
                  <TouchableOpacity style={styles.closeButton} onPress={handleClose}>
                    <Ionicons name="close" size={24} color="#666" />
                  </TouchableOpacity>
                </View>

                {/* Content */}
                <View style={styles.content}>
                  <View style={styles.iconContainer}>
                    <Ionicons name="mail" size={50} color="#2563eb" />
                  </View>
                  
                  <Text style={styles.title}>Verify Your Email</Text>
                  <Text style={styles.subtitle}>
                    📧 OTP bhej diya hai! {email} pe check kar! 🔢
                  </Text>

                  {/* OTP Input */}
                  <View style={styles.otpContainer}>
                    {otp.map((digit, index) => (
                      <TextInput
                        key={index}
                        ref={(ref) => (inputRefs.current[index] = ref)}
                        style={[
                          styles.otpInput,
                          digit ? styles.otpInputFilled : {},
                          otpError ? styles.otpInputError : {},
                        ]}
                        value={digit}
                        onChangeText={(value) => handleOtpChange(value, index)}
                        onKeyPress={({ nativeEvent }) => handleKeyPress(nativeEvent.key, index)}
                        keyboardType="numeric"
                        maxLength={1}
                        textAlign="center"
                        selectTextOnFocus
                      />
                    ))}
                  </View>

                  {/* Error Message */}
                  {otpError ? (
                    <Text style={styles.errorText}>{otpError}</Text>
                  ) : null}

                  {/* Verify Button */}
                  <TouchableOpacity
                    style={[styles.verifyButton, isLoading && styles.verifyButtonDisabled]}
                    onPress={handleVerifyOTP}
                    disabled={isLoading}
                  >
                    <Text style={styles.verifyButtonText}>
                      {isLoading ? '⏳ Verifying...' : '✅ Verify Email'}
                    </Text>
                  </TouchableOpacity>

                  {/* Resend OTP */}
                  <TouchableOpacity
                    style={[styles.resendButton, resendCooldown > 0 && styles.resendButtonDisabled]}
                    onPress={handleResendOTP}
                    disabled={resendCooldown > 0 || isLoading}
                  >
                    <Text style={[styles.resendButtonText, resendCooldown > 0 && styles.resendButtonTextDisabled]}>
                      {resendCooldown > 0 
                        ? `🔄 Resend in ${resendCooldown}s` 
                        : '📧 Resend OTP'
                      }
                    </Text>
                  </TouchableOpacity>
                </View>
              </Animated.View>
            </KeyboardAvoidingView>
          </TouchableWithoutFeedback>
        </Animated.View>
      </TouchableWithoutFeedback>
    </Modal>
  );
};

const styles = StyleSheet.create({
  backdrop: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  keyboardAvoidingView: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  modalContainer: {
    backgroundColor: '#ffffff',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingBottom: Platform.OS === 'ios' ? 34 : 20, // Account for safe area
    maxHeight: height * 0.8,
    elevation: 10,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: -2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  header: {
    alignItems: 'center',
    paddingTop: 12,
    paddingHorizontal: 20,
    position: 'relative',
  },
  dragIndicator: {
    width: 40,
    height: 4,
    backgroundColor: '#e0e0e0',
    borderRadius: 2,
    marginBottom: 10,
  },
  closeButton: {
    position: 'absolute',
    right: 20,
    top: 12,
    padding: 8,
    borderRadius: 20,
    backgroundColor: '#f5f5f5',
  },
  content: {
    paddingHorizontal: 24,
    paddingTop: 10,
    paddingBottom: 20,
    alignItems: 'center',
  },
  iconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#eff6ff',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: '#6b7280',
    textAlign: 'center',
    marginBottom: 30,
    lineHeight: 22,
  },
  otpContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
    paddingHorizontal: 10,
  },
  otpInput: {
    width: 45,
    height: 55,
    borderWidth: 2,
    borderColor: '#e5e7eb',
    borderRadius: 12,
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1f2937',
    backgroundColor: '#ffffff',
    marginHorizontal: 4,
  },
  otpInputFilled: {
    borderColor: '#2563eb',
    backgroundColor: '#eff6ff',
  },
  otpInputError: {
    borderColor: '#ef4444',
    backgroundColor: '#fef2f2',
  },
  errorText: {
    color: '#ef4444',
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 20,
    paddingHorizontal: 10,
  },
  verifyButton: {
    backgroundColor: '#2563eb',
    paddingVertical: 16,
    paddingHorizontal: 32,
    borderRadius: 12,
    width: '100%',
    alignItems: 'center',
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#2563eb',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  verifyButtonDisabled: {
    backgroundColor: '#9ca3af',
    elevation: 0,
    shadowOpacity: 0,
  },
  verifyButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  resendButton: {
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
  },
  resendButtonDisabled: {
    opacity: 0.5,
  },
  resendButtonText: {
    color: '#2563eb',
    fontSize: 14,
    fontWeight: '600',
    textAlign: 'center',
  },
  resendButtonTextDisabled: {
    color: '#9ca3af',
  },
});
