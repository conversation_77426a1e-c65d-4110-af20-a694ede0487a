import { Ionicons } from '@expo/vector-icons';
import React, { useState } from 'react';
import {
    Alert,
    KeyboardAvoidingView,
    Platform,
    ScrollView,
    StyleSheet,
    Text,
    TextInput,
    TouchableOpacity,
    View
} from 'react-native';
import { useAuth } from '../../contexts/AuthContext';
import { apiService } from '../../services/api';
import { PasswordValidationResult } from '../../utils/passwordValidation';
import { PasswordInput } from '../common/PasswordInput';
import { OTPVerificationScreen } from './OTPVerificationScreen';



interface LoginScreenProps {
  onSwitchToRegister: () => void;
}

export const LoginScreen: React.FC<LoginScreenProps> = ({ onSwitchToRegister }) => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [showOTPVerification, setShowOTPVerification] = useState(false);
  const [verificationEmail, setVerificationEmail] = useState('');
  const [passwordValidation, setPasswordValidation] = useState<PasswordValidationResult | null>(null);

  // OTP login states
  const [otpRequired, setOtpRequired] = useState(false);
  const [otp, setOtp] = useState('');
  const [otpError, setOtpError] = useState('');
  const [isOtpLoading, setIsOtpLoading] = useState(false);
  const [loginCredentials, setLoginCredentials] = useState<{email: string, password: string} | null>(null);

  const { login } = useAuth();

  // Password validation handler (basic for login)
  const handlePasswordValidation = (result: PasswordValidationResult) => {
    setPasswordValidation(result);
  };

  const handleLogin = async () => {
    if (!email.trim()) {
      Alert.alert('Email Required', '📧 Email toh daal bhai! Kaise login karenge? 📮');
      return;
    }

    if (!password.trim()) {
      Alert.alert('Password Required', '🔒 Password bhool gaya kya? Daal de! 🔑');
      return;
    }

    if (!isValidEmail(email)) {
      Alert.alert('Invalid Email', '📧 Email format galat hai! @ aur .com toh daal de! 😅');
      return;
    }

    try {
      setIsLoading(true);
      setOtpError('');
      console.log('LoginScreen: 🔐 Starting login process for:', email.trim());

      // Step 1: Try to login directly first
      try {
        await login(email.trim(), password);

        // If login succeeds without error, user email is verified
        console.log('LoginScreen: ✅ Login successful! Email already verified');
        Alert.alert(
          'Welcome Back! 🎉',
          '✅ Login successful! App use kar sakte hai ab! 🚀',
          [{ text: 'Continue', style: 'default' }]
        );
        return; // Exit early - login completed successfully

      } catch (loginError: any) {
        console.log('LoginScreen: 📧 Login failed, checking reason:', loginError.message);

        // Check if error is due to unverified email
        if (loginError.message?.includes('email not verified') ||
            loginError.message?.includes('verify your email') ||
            loginError.message?.includes('not verified')) {

          console.log('LoginScreen: 📧 Email not verified! Sending OTP for verification...');

          // Step 2: Email not verified - send OTP for verification
          try {
            await apiService.resendVerificationOTP(email.trim());
            console.log('LoginScreen: 📧 OTP sent successfully for email verification');

            // Set up OTP requirement state
            setLoginCredentials({ email: email.trim(), password: password });
            setOtpRequired(true);

            Alert.alert(
              'Email Verification Required! 📧',
              '✅ Credentials correct hai! Lekin email verify nahi hai! 🔢\n\nOTP bhej diya - email check kar aur verify kar! 🔑',
              [{ text: 'OK', style: 'default' }]
            );
            return; // Exit - OTP flow started

          } catch (otpError: any) {
            console.error('LoginScreen: ❌ Failed to send OTP:', otpError);

            // Check if error is because email is already verified
            if (otpError.message?.includes('already verified') ||
                otpError.message?.includes('Email is already verified')) {

              console.log('LoginScreen: 📧 Email was already verified! Retrying direct login...');

              // Try login again - email is already verified
              try {
                await login(email.trim(), password);
                console.log('LoginScreen: ✅ Retry login successful after verified email detection');
                Alert.alert(
                  'Welcome Back! 🎉',
                  '✅ Login successful! Email already verified tha! 🚀',
                  [{ text: 'Continue', style: 'default' }]
                );
                return;
              } catch (retryError: any) {
                console.error('LoginScreen: ❌ Retry login failed:', retryError);

                // If retry also fails, show appropriate error
                let errorMessage = '🔒 Login nahi ho raha! Credentials check kar! 🔑';
                if (retryError.message?.includes('Invalid credentials') || retryError.message?.includes('401')) {
                  errorMessage = '❌ Email ya password galat hai! Check kar le! 🔍';
                }

                Alert.alert('Login Failed', errorMessage);
                return;
              }
            } else {
              // Other OTP sending errors
              console.error('LoginScreen: ❌ OTP send error (not verified issue):', otpError.message);
              Alert.alert(
                'OTP Send Failed',
                '🔧 OTP bhejne mein problem! Network check kar aur try again! 🔄',
                [{ text: 'OK', style: 'default' }]
              );
              return;
            }
          }
        } else {
          // Other login errors (invalid credentials, etc.)
          throw loginError;
        }
      }

    } catch (error: any) {
      console.error('LoginScreen: ❌ Login error:', error);

      // Check if error is related to email verification
      if (error.message?.includes('email not verified') || error.message?.includes('verify your email')) {
        Alert.alert(
          'Email Verification Required',
          '📧 Email verify nahi hai! Pehle email verify kar! ✅',
          [
            {
              text: 'Send Verification Code',
              onPress: () => {
                setVerificationEmail(email.trim());
                setShowOTPVerification(true);
              }
            },
            { text: 'Cancel', style: 'cancel' }
          ]
        );
        return;
      }

      let errorMessage = '🔒 Credentials galat hai! Email aur password check kar! 🔑';
      if (error.message?.includes('Invalid credentials') || error.message?.includes('401')) {
        errorMessage = '❌ Email ya password galat hai! Sahi credentials daal! 🔍';
      } else if (error.message?.includes('network') || error.message?.includes('fetch')) {
        errorMessage = '🌐 Internet connection check kar! Network error! 📶';
      } else if (error.message) {
        errorMessage = error.message;
      }

      Alert.alert('Login Failed', errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle OTP verification for login
  const handleOTPLogin = async () => {
    if (!otp.trim()) {
      Alert.alert('OTP Required', '🔢 OTP daal bhai! 6 digit code jo email mein aaya! 📧');
      return;
    }

    if (otp.trim().length !== 6) {
      Alert.alert('Invalid OTP', '🔢 OTP 6 digits ka hona chahiye! Check kar le! ✅');
      return;
    }

    if (!loginCredentials) {
      Alert.alert('Error', 'Login credentials missing. Please try again.');
      return;
    }

    try {
      setIsOtpLoading(true);
      setOtpError('');
      console.log('LoginScreen: 🔐 Verifying OTP for login:', loginCredentials.email);

      // Step 1: Verify OTP using existing endpoint
      const otpResponse = await apiService.verifyEmailOTP(loginCredentials.email, otp.trim());

      console.log('LoginScreen: 📥 OTP verification response:', otpResponse);

      if (otpResponse.success) {
        console.log('LoginScreen: ✅ OTP verified! Completing login...');

        // Step 2: Complete login with verified credentials
        await login(loginCredentials.email, loginCredentials.password);

        console.log('LoginScreen: 🎉 Login successful with OTP verification');

        // Reset all states
        setOtpRequired(false);
        setOtp('');
        setLoginCredentials(null);
        setOtpError('');

        Alert.alert(
          'Welcome Back! 🎉',
          '✅ Login successful! 🚀',
          [{ text: 'Continue', style: 'default' }]
        );
      } else {
        throw new Error(otpResponse.message || 'OTP verification failed');
      }

    } catch (error: any) {
      console.error('LoginScreen: ❌ OTP verification error:', error);

      let errorMessage = 'OTP verify nahi hua! Try again kar! 🔄';
      if (error.message?.includes('Invalid') || error.message?.includes('expired')) {
        errorMessage = '🔢 OTP galat hai ya expire ho gaya! Naya OTP mangwa! ⏰';
      } else if (error.message?.includes('network') || error.message?.includes('fetch')) {
        errorMessage = '🌐 Internet connection check kar! Network error! 📶';
      } else if (error.message) {
        errorMessage = error.message;
      }

      setOtpError(errorMessage);
      Alert.alert('OTP Verification Failed', errorMessage);
    } finally {
      setIsOtpLoading(false);
    }
  };

  const isValidEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  // Show OTP verification screen if needed
  if (showOTPVerification && verificationEmail) {
    return (
      <OTPVerificationScreen
        email={verificationEmail}
        onVerificationSuccess={() => {
          console.log('LoginScreen: OTP verification successful, returning to login');
          setShowOTPVerification(false);
          setVerificationEmail('');
          // User needs to login manually after verification
        }}
        onBackToRegister={() => {
          setShowOTPVerification(false);
          setVerificationEmail('');
        }}
      />
    );
  }

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView contentContainerStyle={styles.scrollContainer}>
        <View style={styles.header}>
          <View style={styles.iconContainer}>
            <Ionicons name="person-circle" size={80} color="#2563eb" />
          </View>
          <Text style={styles.title}>Welcome Back</Text>
          <Text style={styles.subtitle}>Sign in to continue</Text>
        </View>

          <View style={styles.formContainer}>
            <View style={styles.inputContainer}>
              <Ionicons name="mail" size={20} color="#667eea" style={styles.inputIcon} />
              <TextInput
                style={styles.input}
                placeholder="Email Address"
                placeholderTextColor="#999"
                value={email}
                onChangeText={setEmail}
                keyboardType="email-address"
                autoCapitalize="none"
                autoCorrect={false}
              />
            </View>

            {/* Password Input with Basic Validation */}
            <View style={styles.passwordContainer}>
              <View style={styles.inputLabelContainer}>
                <Ionicons name="lock-closed" size={20} color="#667eea" style={styles.inputIcon} />
                <Text style={styles.inputLabel}>Password</Text>
              </View>
              <PasswordInput
                value={password}
                onChangeText={setPassword}
                placeholder="Enter your password"
                validationType="login" // Basic validation for existing passwords
                showStrengthIndicator={false}
                showValidationErrors={false}
                onValidationChange={handlePasswordValidation}
                style={styles.passwordInputField}
              />
            </View>

            {/* Login Button - Changes behavior based on OTP requirement */}
            {!otpRequired ? (
              <TouchableOpacity
                style={[styles.loginButton, isLoading && styles.disabledButton]}
                onPress={handleLogin}
                disabled={isLoading}
              >
                <Text style={styles.loginButtonText}>
                  {isLoading ? 'Verifying Credentials... ⏳' : 'Sign In 🔑'}
                </Text>
              </TouchableOpacity>
            ) : null}

            {/* OTP Input Field - Show only after credentials are verified */}
            {otpRequired && (
              <View style={styles.otpContainer}>
                <View style={styles.inputLabelContainer}>
                  <Ionicons name="shield-checkmark" size={20} color="#667eea" style={styles.inputIcon} />
                  <Text style={styles.inputLabel}>Enter Login OTP</Text>
                  <Text style={styles.otpCounter}>({otp.length}/6)</Text>
                </View>
                <View style={[
                  styles.inputContainer,
                  otp.length === 6 ? styles.inputContainerValid : null
                ]}>
                  <Ionicons name="key" size={20} color="#667eea" style={styles.inputIcon} />
                  <TextInput
                    style={styles.input}
                    placeholder="Enter 6-digit OTP"
                    placeholderTextColor="#999"
                    value={otp}
                    onChangeText={(text) => {
                      // Only allow numeric input
                      const numericText = text.replace(/[^0-9]/g, '');
                      setOtp(numericText);
                      // Clear any previous OTP errors when user starts typing
                      if (otpError) {
                        setOtpError('');
                      }
                    }}
                    keyboardType="numeric"
                    maxLength={6}
                    autoCapitalize="none"
                    autoCorrect={false}
                    autoFocus={true}
                  />
                  {otp.length === 6 && (
                    <Ionicons name="checkmark-circle" size={20} color="#10b981" style={styles.validIcon} />
                  )}
                </View>
                {otpError ? (
                  <Text style={styles.otpError}>{otpError}</Text>
                ) : otp.length === 6 ? (
                  <Text style={styles.otpSuccess}>✅ OTP ready! Click Complete Login</Text>
                ) : (
                  <Text style={styles.otpHelper}>📧 Check your email for the 6-digit code</Text>
                )}
              </View>
            )}

            {/* Complete Login Button - Show only when OTP is required and entered */}
            {otpRequired && (
              <TouchableOpacity
                style={[
                  styles.loginButton,
                  (isOtpLoading || !otp.trim() || otp.trim().length !== 6) && styles.disabledButton
                ]}
                onPress={handleOTPLogin}
                disabled={isOtpLoading || !otp.trim() || otp.trim().length !== 6}
              >
                <Text style={styles.loginButtonText}>
                  {isOtpLoading ? 'Verifying OTP... ⏳' : 'Complete Login 🚀'}
                </Text>
              </TouchableOpacity>
            )}

            {/* Resend OTP option - Show only when OTP is required */}
            {otpRequired && (
              <TouchableOpacity
                style={styles.resendLink}
                onPress={async () => {
                  if (!loginCredentials) return;
                  try {
                    setIsOtpLoading(true);
                    setOtpError('');
                    await apiService.resendVerificationOTP(loginCredentials.email);
                    Alert.alert(
                      'OTP Resent! 📧',
                      '✅ Naya login OTP bhej diya! Email check kar! 🔢'
                    );
                  } catch (error: any) {
                    const errorMessage = error.message?.includes('network')
                      ? '🌐 Internet connection check kar! Network error! 📶'
                      : 'OTP resend nahi hua! Try again kar! 🔄';
                    Alert.alert('Resend Failed', errorMessage);
                  } finally {
                    setIsOtpLoading(false);
                  }
                }}
                disabled={isOtpLoading}
              >
                <Text style={styles.resendText}>
                  Didn't receive OTP? <Text style={styles.resendTextBold}>Resend 🔄</Text>
                </Text>
              </TouchableOpacity>
            )}

            {/* Back to Credentials Button - Show when OTP is required */}
            {otpRequired && (
              <TouchableOpacity
                style={styles.backButton}
                onPress={() => {
                  setOtpRequired(false);
                  setOtp('');
                  setLoginCredentials(null);
                  setOtpError('');
                }}
              >
                {/* <Text style={styles.backButtonText}>
                  ← Back to Email/Password
                </Text> */}
              </TouchableOpacity>
            )}

            <TouchableOpacity
              style={styles.verifyEmailButton}
              onPress={() => {
                if (!email.trim()) {
                  Alert.alert('Email Required', 'Please enter your email address first.');
                  return;
                }
                if (!isValidEmail(email)) {
                  Alert.alert('Invalid Email', 'Please enter a valid email address.');
                  return;
                }
                setVerificationEmail(email.trim());
                setShowOTPVerification(true);
              }}
            >
              <Text >
              </Text>
            </TouchableOpacity>

            <View style={styles.divider}>
              <View style={styles.dividerLine} />
              <Text style={styles.dividerText}>OR</Text>
              <View style={styles.dividerLine} />
            </View>

            <TouchableOpacity
              style={styles.registerLink}
              onPress={onSwitchToRegister}
            >
              <Text style={styles.registerText}>
                Don't have an account? 
                <Text style={styles.registerTextBold}> Sign Up</Text>
              </Text>
            </TouchableOpacity>
          </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  scrollContainer: {
    flexGrow: 1,
    justifyContent: 'center',
    padding: 24,
  },
  header: {
    alignItems: 'center',
    marginBottom: 48,
  },
  iconContainer: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: '#f1f5f9',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
  },
  title: {
    fontSize: 32,
    fontWeight: '700',
    color: '#1e293b',
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: '#64748b',
    textAlign: 'center',
  },
  formContainer: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 24,
    borderWidth: 1,
    borderColor: '#e2e8f0',
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f8fafc',
    borderRadius: 12,
    marginBottom: 16,
    paddingHorizontal: 16,
    borderWidth: 1,
    borderColor: '#e2e8f0',
  },
  inputIcon: {
    marginRight: 12,
    color: '#64748b',
  },
  input: {
    flex: 1,
    height: 52,
    fontSize: 16,
    color: '#1e293b',
  },
  passwordInput: {
    paddingRight: 40,
  },
  eyeIcon: {
    position: 'absolute',
    right: 16,
    padding: 4,
  },
  loginButton: {
    backgroundColor: '#2563eb',
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
    marginBottom: 20,
    marginTop: 8,
  },
  disabledButton: {
    opacity: 0.6,
    backgroundColor: '#94a3b8',
  },
  loginButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },
  verifyEmailButton: {
    paddingVertical: 12,
    paddingHorizontal: 20,
    alignItems: 'center',
    marginBottom: 16,
  },
  verifyEmailText: {
    color: '#2563eb',
    fontSize: 14,
    fontWeight: '500',
    textDecorationLine: 'underline',
  },
  divider: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 24,
  },
  dividerLine: {
    flex: 1,
    height: 1,
    backgroundColor: '#e2e8f0',
  },
  dividerText: {
    marginHorizontal: 16,
    color: '#64748b',
    fontSize: 14,
    fontWeight: '500',
  },
  registerLink: {
    alignItems: 'center',
    marginTop: 8,
  },
  registerText: {
    fontSize: 16,
    color: '#64748b',
  },
  registerTextBold: {
    fontWeight: '600',
    color: '#2563eb',
  },
  passwordContainer: {
    marginBottom: 16,
  },
  inputLabelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
    marginLeft: 8,
  },
  passwordInputField: {
    marginBottom: 0,
  },
  otpContainer: {
    marginBottom: 16,
  },
  otpCounter: {
    color: '#64748b',
    fontSize: 12,
    marginLeft: 'auto',
    fontWeight: '500',
  },
  inputContainerValid: {
    borderColor: '#10b981',
    backgroundColor: '#f0fdf4',
  },
  validIcon: {
    position: 'absolute',
    right: 15,
  },
  otpError: {
    color: '#ef4444',
    fontSize: 12,
    marginTop: 4,
    fontWeight: '500',
  },
  otpSuccess: {
    color: '#10b981',
    fontSize: 12,
    marginTop: 4,
    fontWeight: '500',
    textAlign: 'center',
  },
  otpHelper: {
    color: '#64748b',
    fontSize: 12,
    marginTop: 4,
    textAlign: 'center',
    fontStyle: 'italic',
  },
  resendLink: {
    alignItems: 'center',
    marginTop: 16,
    marginBottom: 8,
  },
  resendText: {
    fontSize: 14,
    color: '#64748b',
  },
  resendTextBold: {
    fontWeight: '600',
    color: '#2563eb',
  },
  backButton: {
    alignItems: 'center',
    marginTop: 8,
    marginBottom: 16,
  },
  backButtonText: {
    fontSize: 14,
    color: '#64748b',
    fontWeight: '500',
  },
});
