import { Ionicons } from '@expo/vector-icons';
import React, { useState } from 'react';
import {
    KeyboardAvoidingView,
    Platform,
    ScrollView,
    StyleSheet,
    Text,
    TextInput,
    TouchableOpacity,
    View
} from 'react-native';
import { useAuth } from '../../contexts/AuthContext';
import { notificationService } from '../../services/notificationService';
import { PasswordValidationResult } from '../../utils/passwordValidation';
import { PasswordInput } from '../common/PasswordInput';
import { OTPModal } from './OTPModal';


interface RegisterScreenProps {
  onSwitchToLogin: () => void;
}

export const RegisterScreen: React.FC<RegisterScreenProps> = ({ onSwitchToLogin }) => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    password: '',
    confirmPassword: '',
    gender: 'male' as 'male' | 'female',
  });

  // Password validation states
  const [passwordValidation, setPasswordValidation] = useState<PasswordValidationResult | null>(null);
  const [confirmPasswordError, setConfirmPasswordError] = useState<string>('');

  // OTP modal states
  const [showOTPModal, setShowOTPModal] = useState(false);
  const [registeredEmail, setRegisteredEmail] = useState('');

  const [isLoading, setIsLoading] = useState(false);
  const { register } = useAuth();

  // Password validation handler
  const handlePasswordValidation = (result: PasswordValidationResult) => {
    setPasswordValidation(result);
  };

  // Confirm password validation
  const validateConfirmPassword = (confirmPassword: string) => {
    if (confirmPassword !== formData.password) {
      setConfirmPasswordError('🔄 Passwords match nahi kar rahe! Same password daal! 🤝');
    } else {
      setConfirmPasswordError('');
    }
  };

  // Handle OTP verification success
  const handleOTPSuccess = () => {
    console.log('RegisterScreen: OTP verification successful, redirecting to login');
    setShowOTPModal(false);
    setRegisteredEmail('');
    // Redirect to login page after successful OTP verification
    setTimeout(() => {
      onSwitchToLogin();
    }, 1500); // Give user time to see the success message
  };

  // Handle OTP modal close
  const handleOTPModalClose = () => {
    console.log('RegisterScreen: OTP modal closed');
    setShowOTPModal(false);
    setRegisteredEmail('');
  };

  // Form validation before submission
  const validateForm = (): boolean => {
    // Check if all fields are filled
    if (!formData.name.trim()) {
      notificationService.error('📝 Name Required', 'Name toh daal bhai! Kya naam hai tera? 👤');
      return false;
    }

    if (!formData.email.trim()) {
      notificationService.error('📧 Email Required', 'Email address chahiye! Kaise contact karenge? 📮');
      return false;
    }

    // Email format validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.email)) {
      notificationService.error('📧 Invalid Email', 'Email format galat hai! @ aur .com toh daal de! 😅');
      return false;
    }

    // Password validation
    if (!passwordValidation || !passwordValidation.isValid) {
      // Show specific password errors
      notificationService.error('🔒 Password Error', 'Password requirements puri nahi hui! Check kar le! ✅');
      return false;
    }

    // Confirm password validation
    if (formData.password !== formData.confirmPassword) {
      notificationService.error('🔄 Password Mismatch', 'Passwords match nahi kar rahe! Same password daal! 🤝');
      return false;
    }

    return true;
  };



  const handleRegister = async () => {
    console.log('RegisterScreen: 🚀 Starting registration process');

    // Use comprehensive validation
    if (!validateForm()) {
      console.log('RegisterScreen: ❌ Form validation failed');
      return;
    }
    console.log('RegisterScreen: ✅ Form validation passed');

    try {
      setIsLoading(true);
      console.log('RegisterScreen: 📡 Starting API registration call with data:', {
        name: formData.name.trim(),
        email: formData.email.trim(),
        gender: formData.gender,
      });

      // Call the registration API
      const result = await register({
        name: formData.name.trim(),
        email: formData.email.trim(),
        password: formData.password,
        gender: formData.gender,
      });

      console.log('RegisterScreen: 📥 Registration API completed successfully');
      console.log('RegisterScreen: Registration result:', {
        requiresVerification: result.requiresVerification,
        email: result.email,
        hasResult: !!result
      });

      // Check if registration was successful
      if (result && result.requiresVerification) {
        console.log('RegisterScreen: ✅ Registration successful - showing OTP modal');

        // Show success notification and show OTP modal
        notificationService.success('🎉 Account created! Email verify kar! OTP bhej diya! 📧');

        // Set up OTP modal
        const emailForOTP = result.email || formData.email.trim();
        setRegisteredEmail(emailForOTP);
        setShowOTPModal(true);

        console.log('RegisterScreen: 🚀 Setting showOTPModal to true for email:', emailForOTP);
      } else {
        console.error('RegisterScreen: ❌ Registration result invalid or missing requiresVerification');
        throw new Error('Registration failed - invalid response');
      }

    } catch (error: any) {
      console.error('RegisterScreen: Registration error:', error);

      let errorMessage = 'Please try again';
      if (error.message?.includes('already exists') || error.message?.includes('duplicate')) {
        errorMessage = 'An account with this email already exists. Please try logging in instead.';
      } else if (error.message?.includes('network') || error.message?.includes('fetch')) {
        errorMessage = 'Network error. Please check your internet connection and try again.';
      } else if (error.message) {
        errorMessage = error.message;
      }

      notificationService.error('❌ Registration Failed', errorMessage);
    } finally {
      setIsLoading(false);
    }
  };



  const updateFormData = (field: string, value: string) => {
    console.log(`Updating ${field} to:`, value);
    setFormData(prev => ({ ...prev, [field]: value }));
  };



  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView contentContainerStyle={styles.scrollContainer}>
        <View style={styles.header}>
          <View style={styles.iconContainer}>
            <Ionicons name="person-add" size={80} color="#2563eb" />
          </View>
          <Text style={styles.title}>Create Account</Text>
          <Text style={styles.subtitle}>Join us today</Text>
        </View>

          <View style={styles.formContainer}>
            <View style={styles.inputContainer}>
              <Ionicons name="person" size={20} color="#667eea" style={styles.inputIcon} />
              <TextInput
                style={styles.input}
                placeholder="Full Name"
                placeholderTextColor="#999"
                value={formData.name}
                onChangeText={(value) => updateFormData('name', value)}
                autoCapitalize="words"
                autoCorrect={false}
              />
            </View>

            <View style={styles.inputContainer}>
              <Ionicons name="mail" size={20} color="#667eea" style={styles.inputIcon} />
              <TextInput
                style={styles.input}
                placeholder="Email Address"
                placeholderTextColor="#999"
                value={formData.email}
                onChangeText={(value) => updateFormData('email', value)}
                keyboardType="email-address"
                autoCapitalize="none"
                autoCorrect={false}
              />
            </View>

            <View style={styles.genderContainer}>
              <Text style={styles.genderLabel}>Gender (Current: {formData.gender})</Text>
              <View style={styles.genderButtons}>
                <TouchableOpacity
                  style={[
                    styles.genderButton,
                    formData.gender === 'male' && styles.genderButtonActive
                  ]}
                  onPress={() => updateFormData('gender', 'male')}
                >
                  <Ionicons
                    name="man"
                    size={20}
                    color={formData.gender === 'male' ? '#fff' : '#667eea'}
                  />
                  <Text style={[
                    styles.genderButtonText,
                    formData.gender === 'male' && styles.genderButtonTextActive
                  ]}>
                    Male
                  </Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={[
                    styles.genderButton,
                    formData.gender === 'female' && styles.genderButtonActive
                  ]}
                  onPress={() => updateFormData('gender', 'female')}
                >
                  <Ionicons
                    name="woman"
                    size={20}
                    color={formData.gender === 'female' ? '#fff' : '#667eea'}
                  />
                  <Text style={[
                    styles.genderButtonText,
                    formData.gender === 'female' && styles.genderButtonTextActive
                  ]}>
                    Female
                  </Text>
                </TouchableOpacity>
              </View>
            </View>

            {/* Password Input with Validation */}
            <View style={styles.passwordContainer}>
              <View style={styles.inputLabelContainer}>
                <Ionicons name="lock-closed" size={20} color="#667eea" style={styles.inputIcon} />
                <Text style={styles.inputLabel}>Password</Text>
              </View>
              <PasswordInput
                value={formData.password}
                onChangeText={(value) => updateFormData('password', value)}
                placeholder="Create a strong password"
                validationType="register"
                showStrengthIndicator={true}
                showValidationErrors={true}
                onValidationChange={handlePasswordValidation}
                style={styles.passwordInputField}
              />
            </View>

            {/* Confirm Password Input */}
            <View style={styles.passwordContainer}>
              <View style={styles.inputLabelContainer}>
                <Ionicons name="lock-closed" size={20} color="#667eea" style={styles.inputIcon} />
                <Text style={styles.inputLabel}>Confirm Password</Text>
              </View>
              <PasswordInput
                value={formData.confirmPassword}
                onChangeText={(value) => {
                  updateFormData('confirmPassword', value);
                  validateConfirmPassword(value);
                }}
                placeholder="Confirm your password"
                validationType="login" // No validation rules, just basic input
                showStrengthIndicator={false}
                showValidationErrors={false}
                style={styles.passwordInputField}
              />
              {confirmPasswordError ? (
                <Text style={styles.confirmPasswordError}>{confirmPasswordError}</Text>
              ) : null}
            </View>

            <TouchableOpacity
              style={[styles.registerButton, isLoading && styles.disabledButton]}
              onPress={handleRegister}
              disabled={isLoading}
            >
              <Text style={styles.registerButtonText}>
                {isLoading ? 'Creating Account...' : 'Create Account'}
              </Text>
            </TouchableOpacity>

            <View style={styles.divider}>
              <View style={styles.dividerLine} />
              <Text style={styles.dividerText}>OR</Text>
              <View style={styles.dividerLine} />
            </View>

            <TouchableOpacity
              style={styles.loginLink}
              onPress={onSwitchToLogin}
            >
              <Text style={styles.loginText}>
                Already have an account?
                <Text style={styles.loginTextBold}> Sign In</Text>
              </Text>
            </TouchableOpacity>
          </View>
      </ScrollView>

      {/* OTP Verification Modal */}
      <OTPModal
        visible={showOTPModal}
        email={registeredEmail}
        onVerificationSuccess={handleOTPSuccess}
        onClose={handleOTPModalClose}
      />
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  scrollContainer: {
    flexGrow: 1,
    justifyContent: 'center',
    padding: 24,
  },
  header: {
    alignItems: 'center',
    marginBottom: 48,
  },
  iconContainer: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: '#f1f5f9',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
  },
  title: {
    fontSize: 32,
    fontWeight: '700',
    color: '#1e293b',
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: '#64748b',
    textAlign: 'center',
  },
  formContainer: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 24,
    borderWidth: 1,
    borderColor: '#e2e8f0',
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f8fafc',
    borderRadius: 12,
    marginBottom: 16,
    paddingHorizontal: 16,
    borderWidth: 1,
    borderColor: '#e2e8f0',
  },
  inputIcon: {
    marginRight: 12,
    color: '#64748b',
  },
  input: {
    flex: 1,
    height: 52,
    fontSize: 16,
    color: '#1e293b',
  },
  passwordInput: {
    paddingRight: 40,
  },
  eyeIcon: {
    position: 'absolute',
    right: 15,
    padding: 5,
  },
  genderContainer: {
    marginBottom: 20,
  },
  genderLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 10,
  },
  genderButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  genderButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 14,
    paddingHorizontal: 20,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#e2e8f0',
    backgroundColor: '#f8fafc',
  },
  genderButtonActive: {
    backgroundColor: '#2563eb',
    borderColor: '#2563eb',
  },
  genderButtonText: {
    marginLeft: 8,
    fontSize: 16,
    fontWeight: '500',
    color: '#64748b',
  },
  genderButtonTextActive: {
    color: '#ffffff',
  },
  registerButton: {
    backgroundColor: '#2563eb',
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
    marginBottom: 20,
    marginTop: 8,
  },
  disabledButton: {
    opacity: 0.6,
    backgroundColor: '#94a3b8',
  },
  registerButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },
  divider: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 24,
  },
  dividerLine: {
    flex: 1,
    height: 1,
    backgroundColor: '#e2e8f0',
  },
  dividerText: {
    marginHorizontal: 16,
    color: '#64748b',
    fontSize: 14,
    fontWeight: '500',
  },
  loginLink: {
    alignItems: 'center',
    marginTop: 8,
  },
  loginText: {
    fontSize: 16,
    color: '#64748b',
  },
  loginTextBold: {
    fontWeight: '600',
    color: '#2563eb',
  },
  passwordContainer: {
    marginBottom: 16,
  },
  inputLabelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
    marginLeft: 8,
  },
  passwordInputField: {
    marginBottom: 0,
  },
  confirmPasswordError: {
    color: '#ef4444',
    fontSize: 12,
    marginTop: 4,
    fontWeight: '500',
  },
});
